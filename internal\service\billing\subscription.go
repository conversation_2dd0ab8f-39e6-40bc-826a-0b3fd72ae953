package billing

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Subscription CRUD operations
func (s *service) CreateSubscription(ctx context.Context, userID, planID primitive.ObjectID, provider billing.PaymentProvider) (*billing.Subscription, error) {
	// Get user email for the subscription
	user, err := s.userRepository.Find(ctx, userID)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to find user for subscription", errors.Internal, err)
	}

	return s.createSubscriptionInternal(ctx, &userID, user.Email, planID, provider)
}

func (s *service) CreateSubscriptionWithEmail(ctx context.Context, userEmail string, planID primitive.ObjectID, provider billing.PaymentProvider) (*billing.Subscription, error) {
	return s.createSubscriptionInternal(ctx, nil, userEmail, planID, provider)
}

// Internal method to create subscriptions with optional userID
func (s *service) createSubscriptionInternal(ctx context.Context, userID *primitive.ObjectID, userEmail string, planID primitive.ObjectID, provider billing.PaymentProvider) (*billing.Subscription, error) {
	// Validate the plan
	plan, err := s.ValidatePlanForSubscription(ctx, planID)
	if err != nil {
		return nil, err
	}

	// Create subscription with plan defaults
	now := time.Now()
	subscription := &billing.Subscription{
		UserEmail: userEmail,
		PlanID:    planID,
		Provider:  provider,
		Status:    billing.SubscriptionStatusTrial, // Start with trial
		StartDate: now,
		EndDate:   billing.SubscriptionHelperInstance.CalculateEndDate(now, plan.DurationMonths),
		AutoRenew: plan.AutoRenew,
	}

	// Set UserID if provided
	if userID != nil {
		subscription.UserID = *userID
	}

	// Set trial end date if plan has trial period
	if plan.TrialDays > 0 {
		subscription.TrialEndDate = billing.SubscriptionHelperInstance.CalculateTrialEndDate(now, plan.TrialDays)
	} else {
		// No trial, start as active
		subscription.Status = billing.SubscriptionStatusActive
	}

	subscription.SetDefaults()

	// Create the subscription
	subscriptionID, err := s.repo.Subscriptions().Create(ctx, subscription)
	if err != nil {
		return nil, err
	}

	// Convert string ID back to ObjectID for return
	objectID, err := primitive.ObjectIDFromHex(subscriptionID)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to convert subscription ID", errors.Internal, err)
	}

	subscription.ObjectID = objectID
	subscription.ID = subscriptionID

	return subscription, nil
}

func (s *service) FindSubscription(ctx context.Context, id primitive.ObjectID) (*billing.Subscription, error) {
	return s.repo.Subscriptions().Find(ctx, id)
}

func (s *service) FindUserSubscriptions(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error) {
	return s.repo.Subscriptions().FindByUser(ctx, userID)
}

func (s *service) FindActiveUserSubscriptions(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error) {
	return s.repo.Subscriptions().FindActiveByUser(ctx, userID)
}

func (s *service) FindSubscriptionsByEmail(ctx context.Context, email string) ([]*billing.Subscription, error) {
	return s.repo.Subscriptions().FindByEmail(ctx, email)
}

func (s *service) UpdateSubscription(ctx context.Context, subscription *billing.Subscription) error {
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) UpdateSubscriptionWithUserID(ctx context.Context, subscriptionID, userID primitive.ObjectID) error {
	// Find the subscription
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	// Update the UserID
	subscription.UserID = userID
	subscription.UpdatedAt = time.Now()

	// Update the subscription
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) LinkExistingSubscriptionsToUser(ctx context.Context, userID primitive.ObjectID, email string) error {
	// Find all subscriptions for this email that don't have a user ID
	subscriptions, err := s.repo.Subscriptions().FindByEmail(ctx, email)
	if err != nil {
		return err
	}

	// Update subscriptions that don't have a user ID
	for _, subscription := range subscriptions {
		if subscription.UserID.IsZero() {
			subscription.UserID = userID
			subscription.UpdatedAt = time.Now()

			if err := s.repo.Subscriptions().Update(ctx, subscription); err != nil {
				// Log the error but continue with other subscriptions
				log.Printf("Failed to link subscription %s to user %s: %v", subscription.ID, userID.Hex(), err)
			}
		}
	}

	return nil
}

func (s *service) DeleteSubscription(ctx context.Context, id primitive.ObjectID) error {
	// Check if subscription exists
	subscription, err := s.repo.Subscriptions().Find(ctx, id)
	if err != nil {
		return err
	}

	// Check if subscription has payments
	payments, err := s.repo.Payments().FindBySubscription(ctx, id)
	if err != nil {
		return errors.New(errors.Service, "failed to check for subscription payments", errors.Internal, err)
	}

	if len(payments) > 0 {
		// Cancel instead of delete if payments exist
		subscription.Cancel("Subscription deleted by admin")
		return s.repo.Subscriptions().Update(ctx, subscription)
	}

	return s.repo.Subscriptions().Delete(ctx, id)
}

// Subscription management operations
func (s *service) CancelSubscription(ctx context.Context, subscriptionID primitive.ObjectID, reason string) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if subscription.Status == billing.SubscriptionStatusCancelled {
		return errors.New(errors.Service, "subscription is already cancelled", errors.Validation, nil)
	}

	if subscription.Status == billing.SubscriptionStatusExpired {
		return errors.New(errors.Service, "cannot cancel expired subscription", errors.Validation, nil)
	}

	// Business logic: Handle cancellation based on trial period
	// Case 1: Cancelled during trial period - revoke access immediately
	if subscription.IsInTrial() {
		subscription.Cancel(reason + " (Trial cancellation - immediate access revocation)")
		return s.repo.Subscriptions().Update(ctx, subscription)
	}

	// Case 2: Cancelled after trial period - maintain access until end date
	subscription.Cancel(reason + " (Post-trial cancellation - access retained until end date)")
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) SuspendSubscription(ctx context.Context, subscriptionID primitive.ObjectID) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if subscription.Status == billing.SubscriptionStatusSuspended {
		return errors.New(errors.Service, "subscription is already suspended", errors.Validation, nil)
	}

	if subscription.Status == billing.SubscriptionStatusExpired || subscription.Status == billing.SubscriptionStatusCancelled {
		return errors.New(errors.Service, "cannot suspend expired or cancelled subscription", errors.Validation, nil)
	}

	subscription.Suspend()
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) ReactivateSubscription(ctx context.Context, subscriptionID primitive.ObjectID) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if subscription.Status != billing.SubscriptionStatusSuspended {
		return errors.New(errors.Service, "only suspended subscriptions can be reactivated", errors.Validation, nil)
	}

	// Check if subscription hasn't expired
	if time.Now().After(subscription.EndDate) {
		return errors.New(errors.Service, "cannot reactivate expired subscription", errors.Validation, nil)
	}

	subscription.Reactivate()
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) RenewSubscription(ctx context.Context, subscriptionID primitive.ObjectID) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if !subscription.AutoRenew {
		return errors.New(errors.Service, "subscription is not set to auto-renew", errors.Validation, nil)
	}

	// Get the plan to determine renewal duration
	plan, err := s.repo.Plans().Find(ctx, subscription.PlanID)
	if err != nil {
		return err
	}

	if !plan.IsActive() {
		return errors.New(errors.Service, "cannot renew subscription for inactive plan", errors.Validation, nil)
	}

	// Extend the subscription
	subscription.EndDate = billing.SubscriptionHelperInstance.CalculateEndDate(subscription.EndDate, plan.DurationMonths)
	subscription.Status = billing.SubscriptionStatusActive
	subscription.TrialEndDate = nil // No trial for renewals

	return s.repo.Subscriptions().Update(ctx, subscription)
}

// Subscription lookup operations
func (s *service) FindSubscriptionByProviderID(ctx context.Context, provider billing.PaymentProvider, providerSubscriptionID string) (*billing.Subscription, error) {
	if providerSubscriptionID == "" {
		return nil, errors.New(errors.Service, "provider subscription ID is required", errors.Validation, nil)
	}

	return s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, provider, providerSubscriptionID)
}

func (s *service) FindExpiringSubscriptions(ctx context.Context, days int) ([]*billing.Subscription, error) {
	if days < 0 {
		return nil, errors.New(errors.Service, "days must be non-negative", errors.Validation, nil)
	}

	return s.repo.Subscriptions().FindExpiring(ctx, days)
}

// Access control operations
func (s *service) HasActiveSubscription(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	subscriptions, err := s.repo.Subscriptions().FindActiveByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check if any subscription is truly active (not just in database)
	for _, subscription := range subscriptions {
		if subscription.IsActive() {
			return true, nil
		}
	}

	return false, nil
}

func (s *service) GetUserFeatures(ctx context.Context, userID primitive.ObjectID) ([]string, error) {
	subscriptions, err := s.repo.Subscriptions().FindActiveByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	featuresMap := make(map[string]bool)

	// Collect features from all active subscriptions
	for _, subscription := range subscriptions {
		if subscription.IsActive() {
			plan, err := s.repo.Plans().Find(ctx, subscription.PlanID)
			if err != nil {
				continue // Skip if plan not found
			}

			for _, feature := range plan.Features {
				featuresMap[feature] = true
			}
		}
	}

	// Convert map to slice
	features := make([]string, 0, len(featuresMap))
	for feature := range featuresMap {
		features = append(features, feature)
	}

	return features, nil
}

// Has DinboraPlus feature
func (s *service) HasDinboraPlusFeature(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	features, err := s.GetUserFeatures(ctx, userID)
	if err != nil {
		return false, err
	}

	for _, feature := range features {
		if feature == billing.FeatureDinboraPlus {
			return true, nil
		}
	}

	return false, nil
}
